#!/usr/bin/env python3
"""
二次开发文件索引检查工具

用于验证 SECONDARY_DEVELOPMENT_INDEX.md 的完整性和准确性
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Set
from datetime import datetime

class DevIndexChecker:
    def __init__(self):
        self.root_path = Path(".")
        self.index_file = self.root_path / "docs" / "SECONDARY_DEVELOPMENT_INDEX.md"
        self.secondary_dev_dirs = [
            "pkg/core/workflow",
            "pkg/core/session", 
            "pkg/core/intent",
            "pkg/core/image",
            "pkg/core/message",
            "pkg/workers/flux",
            "pkg/workers/kontext",
            "pkg/workers/kontext_api", 
            "pkg/workers/shared",
            "pkg/provider/runners",
            "pkg/adapters",
            "pkg/pipeline",
            "pkg/processors",
            "pkg/routers",
            "pkg/services",
            "pkg/command/operators"
        ]
        
    def scan_secondary_dev_files(self) -> Dict[str, List[str]]:
        """扫描所有二次开发文件"""
        files_by_dir = {}
        
        for dir_path in self.secondary_dev_dirs:
            full_path = self.root_path / dir_path
            if not full_path.exists():
                continue
                
            files = []
            for file_path in full_path.glob("*.py"):
                # 排除 __init__.py 和 __pycache__ 
                if file_path.name not in ["__init__.py"] and not file_path.name.startswith("__"):
                    files.append(file_path.name)
            
            if files:
                files_by_dir[dir_path] = sorted(files)
                
        return files_by_dir
    
    def parse_index_file(self) -> Dict[str, Set[str]]:
        """解析索引文件中记录的文件"""
        if not self.index_file.exists():
            print(f"❌ 索引文件不存在: {self.index_file}")
            return {}
            
        indexed_files = {}
        current_section = None
        
        with open(self.index_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 提取表格中的文件名
        table_pattern = r'\| `([^`]+\.py)` \|'
        matches = re.findall(table_pattern, content)
        
        # 根据文件名推断目录
        for filename in matches:
            for dir_path in self.secondary_dev_dirs:
                full_path = self.root_path / dir_path / filename
                if full_path.exists():
                    if dir_path not in indexed_files:
                        indexed_files[dir_path] = set()
                    indexed_files[dir_path].add(filename)
                    break
                    
        return indexed_files
    
    def check_completeness(self) -> bool:
        """检查索引完整性"""
        print("🔍 检查二次开发文件索引完整性...")
        print("=" * 50)
        
        actual_files = self.scan_secondary_dev_files()
        indexed_files = self.parse_index_file()
        
        all_good = True
        total_actual = 0
        total_indexed = 0
        
        # 检查每个目录
        for dir_path in sorted(set(actual_files.keys()) | set(indexed_files.keys())):
            actual = set(actual_files.get(dir_path, []))
            indexed = set(indexed_files.get(dir_path, []))
            
            total_actual += len(actual)
            total_indexed += len(indexed)
            
            print(f"\n📁 {dir_path}")
            
            # 检查缺失的文件
            missing = actual - indexed
            if missing:
                all_good = False
                print(f"  ❌ 索引中缺失的文件: {', '.join(sorted(missing))}")
            
            # 检查多余的文件
            extra = indexed - actual
            if extra:
                all_good = False
                print(f"  ⚠️  索引中多余的文件: {', '.join(sorted(extra))}")
            
            # 显示正确的文件
            correct = actual & indexed
            if correct:
                print(f"  ✅ 正确索引的文件: {len(correct)}个")
        
        print(f"\n📊 统计信息:")
        print(f"  实际文件数: {total_actual}")
        print(f"  索引文件数: {total_indexed}")
        print(f"  覆盖率: {(total_indexed/total_actual*100):.1f}%" if total_actual > 0 else "  覆盖率: 0%")
        
        return all_good
    
    def generate_missing_entries(self):
        """生成缺失文件的索引条目"""
        actual_files = self.scan_secondary_dev_files()
        indexed_files = self.parse_index_file()
        
        print("\n📝 缺失文件的索引条目模板:")
        print("=" * 50)
        
        for dir_path in sorted(actual_files.keys()):
            actual = set(actual_files[dir_path])
            indexed = set(indexed_files.get(dir_path, []))
            missing = actual - indexed
            
            if missing:
                print(f"\n### {dir_path}")
                for filename in sorted(missing):
                    print(f"| `{filename}` | 🔧 [功能描述待补充] | ✅ 稳定 | {datetime.now().strftime('%Y-%m-%d')} |")
    
    def check_file_status(self):
        """检查文件的实际状态"""
        print("\n🔍 检查文件状态...")
        print("=" * 50)
        
        actual_files = self.scan_secondary_dev_files()
        
        for dir_path, files in actual_files.items():
            print(f"\n📁 {dir_path} ({len(files)}个文件)")
            for filename in sorted(files):
                file_path = self.root_path / dir_path / filename
                if file_path.exists():
                    # 获取文件修改时间
                    mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    size = file_path.stat().st_size
                    
                    # 判断活跃度
                    days_ago = (datetime.now() - mtime).days
                    if days_ago <= 7:
                        status = "🔥 活跃"
                    elif days_ago <= 30:
                        status = "✅ 稳定"
                    else:
                        status = "😴 静态"
                    
                    print(f"  {filename:<30} {status} ({size:>6}B, {days_ago:>3}天前)")

def main():
    checker = DevIndexChecker()
    
    print("🚀 二次开发文件索引检查工具")
    print("=" * 50)
    
    # 检查完整性
    is_complete = checker.check_completeness()
    
    if is_complete:
        print("\n🎉 索引文件完整且准确！")
    else:
        print("\n⚠️  发现索引问题，需要更新")
        checker.generate_missing_entries()
    
    # 检查文件状态
    checker.check_file_status()
    
    print(f"\n📋 维护建议:")
    print(f"  1. 定期运行此脚本检查索引完整性")
    print(f"  2. 添加新文件时及时更新索引")
    print(f"  3. 删除文件时同步更新索引")
    print(f"  4. 重大修改后更新文件状态")

if __name__ == "__main__":
    main()
