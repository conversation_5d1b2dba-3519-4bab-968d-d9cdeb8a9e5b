# KONTEXT精确图片数量路由更新

## 📋 更新概述

根据用户需求，将KONTEXT管道的路由系统从模糊的"单图/多图"概念改为按具体图片数量的精确路由：1张图、2张图、3张图。

## 🎯 更新目标

- **明确性**: 不再使用模糊的"多图"概念，而是明确按1、2、3张图片数量区分
- **精确性**: 每个图片数量对应特定的工作流文件
- **一致性**: 与现有的 `LocalKontextWorkflowManager` 保持一致
- **扩展性**: 为未来支持更多图片数量预留空间

## 🔧 具体修改

### 1. 枚举值更新

**修改前:**
```python
# KONTEXT管道
KONTEXT_SINGLE = "kontext_single"             # 单图处理
KONTEXT_MULTIPLE = "kontext_multiple"         # 多图处理

# KONTEXT_API管道
KONTEXT_API_SINGLE = "kontext_api_single"     # 单图API
KONTEXT_API_MULTIPLE = "kontext_api_multiple" # 多图API
```

**修改后:**
```python
# KONTEXT管道 - 按具体图片数量区分
KONTEXT_1IMAGE = "kontext_1image"             # 1张图片处理
KONTEXT_2IMAGE = "kontext_2image"             # 2张图片处理
KONTEXT_3IMAGE = "kontext_3image"             # 3张图片处理

# KONTEXT_API管道 - 按具体图片数量区分
KONTEXT_API_1IMAGE = "kontext_api_1image"     # 1张图片API
KONTEXT_API_2IMAGE = "kontext_api_2image"     # 2张图片API
KONTEXT_API_3IMAGE = "kontext_api_3image"     # 3张图片API
```

### 2. 工作流文件映射更新

**修改前:**
```python
WorkflowSubType.KONTEXT_SINGLE: "kontext_local_1image.json",
WorkflowSubType.KONTEXT_MULTIPLE: "kontext_local_2images.json",
WorkflowSubType.KONTEXT_API_SINGLE: "kontext_api_1image.json",
WorkflowSubType.KONTEXT_API_MULTIPLE: "kontext_api_2images.json"
```

**修改后:**
```python
WorkflowSubType.KONTEXT_1IMAGE: "kontext_local_single_image.json",
WorkflowSubType.KONTEXT_2IMAGE: "kontext_local_double_images.json",
WorkflowSubType.KONTEXT_3IMAGE: "kontext_local_triple_images.json",
WorkflowSubType.KONTEXT_API_1IMAGE: "kontext_api_1image.json",
WorkflowSubType.KONTEXT_API_2IMAGE: "kontext_api_2images.json",
WorkflowSubType.KONTEXT_API_3IMAGE: "kontext_api_3images.json"
```

### 3. 路由逻辑更新

**KONTEXT管道路由逻辑:**
```python
def _route_kontext_pipeline(self, has_images: bool, image_count: int):
    if not has_images or image_count == 0:
        return WorkflowSubType.KONTEXT_1IMAGE  # 需要询问
    
    if image_count == 1:
        return WorkflowSubType.KONTEXT_1IMAGE
    elif image_count == 2:
        return WorkflowSubType.KONTEXT_2IMAGE
    elif image_count == 3:
        return WorkflowSubType.KONTEXT_3IMAGE
    else:
        # 超过3张图片，使用3图工作流并提示用户
        return WorkflowSubType.KONTEXT_3IMAGE
```

**KONTEXT_API管道路由逻辑:**
```python
def _route_kontext_api_pipeline(self, has_images: bool, image_count: int):
    if image_count == 1:
        return WorkflowSubType.KONTEXT_API_1IMAGE
    elif image_count == 2:
        return WorkflowSubType.KONTEXT_API_2IMAGE
    elif image_count == 3:
        return WorkflowSubType.KONTEXT_API_3IMAGE
    else:
        return WorkflowSubType.KONTEXT_API_3IMAGE  # 超过3张使用3图工作流
```

### 4. LLM分析提示词更新

更新了KONTEXT_API的LLM分析系统提示词，明确支持1、2、3张图片的不同工作流选项。

### 5. 映射函数更新

更新了 `_map_llm_result_to_subtype` 函数，支持新的枚举值映射。

## 📁 修改的文件

1. **pkg/core/workflow/unified_routing_system.py** - 主要修改文件
   - 更新 `WorkflowSubType` 枚举
   - 更新工作流文件映射
   - 更新路由逻辑
   - 更新LLM分析逻辑

2. **docs/architecture/CORRECTED_ROUTING_MECHANISM.md** - 文档更新
   - 更新路由逻辑示例
   - 更新工作流文件映射
   - 更新测试用例

## 🧪 测试验证

创建并运行了完整的测试套件，验证了：

- ✅ 枚举值正确性
- ✅ 工作流文件映射正确性
- ✅ 路由逻辑正确性
- ✅ 映射函数正确性
- ✅ 边界情况处理（超过3张图片）

## 🎯 使用示例

### KONTEXT管道
- **1张图片**: `kontext 修改背景` + 1张图 → `KONTEXT_1IMAGE` → `kontext_local_single_image.json`
- **2张图片**: `kontext 对比处理` + 2张图 → `KONTEXT_2IMAGE` → `kontext_local_double_images.json`
- **3张图片**: `kontext 序列处理` + 3张图 → `KONTEXT_3IMAGE` → `kontext_local_triple_images.json`

### KONTEXT_API管道
- **1张图片**: `kontext_api 专业处理` + 1张图 → `KONTEXT_API_1IMAGE` → `kontext_api_1image.json`
- **2张图片**: `kontext_api 批量处理` + 2张图 → `KONTEXT_API_2IMAGE` → `kontext_api_2images.json`
- **3张图片**: `kontext_api 复杂处理` + 3张图 → `KONTEXT_API_3IMAGE` → `kontext_api_3images.json`

## 🔄 向后兼容性

- 保持了与现有 `LocalKontextWorkflowManager` 的一致性
- 工作流文件名与实际文件保持一致
- 路由逻辑更加精确和明确

## 📈 优势

1. **精确性**: 明确区分1、2、3张图片的处理方式
2. **一致性**: 与现有Kontext管理器的设计保持一致
3. **可维护性**: 代码更清晰，易于理解和维护
4. **扩展性**: 为未来支持更多图片数量预留了空间
5. **用户友好**: 用户可以明确知道每种情况下使用的工作流

## ✅ 完成状态

- [x] 更新枚举值定义
- [x] 更新工作流文件映射
- [x] 更新KONTEXT管道路由逻辑
- [x] 更新KONTEXT_API管道路由逻辑
- [x] 更新LLM分析逻辑
- [x] 更新映射函数
- [x] 更新相关文档
- [x] 创建并运行测试验证
- [x] 验证所有功能正常工作

这次更新成功地将KONTEXT管道从模糊的"多图"概念改为精确的按图片数量路由，提高了系统的准确性和用户体验。
