
# 🤖 LangBot - 智能多模态聊天机器人

<div align="center">

![LangBot Logo](res/logo.png)

**基于LangBot的二次开发项目，集成微信、ComfyUI图像生成和智能路由系统**

[![Python](https://img.shields.io/badge/python-3.10+-blue.svg)](https://python.org)
[![Docker](https://img.shields.io/badge/docker-supported-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

[📖 文档中心](docs/README.md) | [🚀 快速开始](docs/deployment/QUICK_START.md) | [🔧 开发指南](docs/deployment/DEV_GUIDE.md) | [🤝 贡献指南](docs/CONTRIBUTING.md)

</div>

## ✨ 核心特性

### 🎨 智能图像生成
- **多工作流支持**: 集成Flux、Kontext等多种AI图像生成工作流
- **智能路由系统**: 基于LLM的两级路由架构，自动选择最适合的生成管道
- **ComfyUI集成**: 深度集成ComfyUI，支持复杂的图像处理工作流

### 💬 多平台聊天
- **微信集成**: 支持WeChatPad适配器，实现微信群聊和私聊
- **智能对话**: 基于大语言模型的自然语言理解和响应
- **多模态支持**: 支持文本、图片等多种输入格式

### 🧠 智能路由
- **两级触发机制**: 群聊@机器人+触发词，私聊直接触发词
- **LLM分析**: 智能理解用户意图，自动选择合适的工作流
- **参数优化**: 自动优化提示词，提升生成质量

### 🔧 开发友好
- **模块化架构**: 清晰的代码结构，易于扩展和维护
- **Docker支持**: 完整的容器化部署方案
- **配置灵活**: 支持多种配置方式，适应不同部署需求

## 🚀 快速开始

### 环境要求
- Docker & Docker Compose
- Python 3.10+
- 8GB+ RAM (推荐)

### 一键部署

```bash
# 克隆项目
git clone https://github.com/blueraincoatli/langbot.git
cd langbot

# 启动所有服务
./start-all-services.sh
```

### 手动部署

```bash
# 1. 启动WeChatPad
docker-compose -f wechatpad-docker-compose.yml up -d

# 2. 等待15秒后启动LangBot
sleep 15
docker-compose up -d

# 3. 检查服务状态
docker-compose ps
```

详细部署说明请参考 [快速启动指南](docs/deployment/QUICK_START.md)

### 配置说明

1. **微信配置**: 参考 [WeChatPad配置指南](docs/deployment/DEV_GUIDE.md#微信集成配置)
2. **ComfyUI配置**: 参考 [ComfyUI集成指南](docs/COMFYUI_INTEGRATION.md)
3. **LLM配置**: 配置DeepSeek或其他大模型API密钥

### 服务访问

- **LangBot WebUI**: http://localhost:5300
- **WeChatPad管理**: http://localhost:19088 (如果使用WeChatPad)

## 🏗️ 系统架构

```mermaid
graph TB
    A[微信消息] --> B[WeChatPad适配器]
    B --> C[LangBot核心]
    C --> D[统一路由系统]
    D --> E[LLM分析]
    D --> F[工作流选择]
    F --> G[Flux工作流]
    F --> H[Kontext工作流]
    F --> I[Kontext API工作流]
    G --> J[ComfyUI]
    H --> J
    I --> K[远程ComfyUI API]
    J --> L[图像生成]
    K --> L
    L --> C
    C --> B
    B --> A
```

### 核心组件

- **统一路由系统**: 智能分析用户意图，选择最适合的工作流
- **工作流管理**: 支持多种图像生成工作流，可扩展
- **平台适配器**: 支持多种聊天平台，当前主要支持微信
- **ComfyUI集成**: 深度集成ComfyUI，支持复杂的图像处理管道

## 📖 使用指南

### 基本使用

1. **触发图像生成**:
   - 群聊: `@机器人 aigen 生成一只可爱的猫咪`
   - 私聊: `aigen 生成一只可爱的猫咪`

2. **工作流类型**:
   - `aigen`: 文生图工作流（Flux本地）
   - `kontext`: 图生图工作流（本地Kontext）
   - `kontext_api`: 远程API工作流

3. **执行生成**:
   - 发送触发词后，机器人会提示发送"开始"或"go"
   - 发送"开始"后开始图像生成

### 支持的平台

| 平台 | 状态 | 备注 |
| --- | --- | --- |
| 个人微信 | ✅ | 通过WeChatPad适配器 |
| 企业微信 | ✅ | 原生支持 |
| QQ | ✅ | 原生支持 |
| 其他平台 | 🔄 | 基于LangBot框架可扩展 |

### 支持的大模型

| 模型 | 状态 | 备注 |
| --- | --- | --- |
| [DeepSeek](https://www.deepseek.com/) | ✅ | 主要使用，性价比高 |
| [OpenAI](https://platform.openai.com/) | ✅ | 兼容OpenAI接口格式 |
| [Qwen](https://dashscope.aliyun.com/) | ✅ | 阿里云通义千问 |
| [智谱AI](https://open.bigmodel.cn/) | ✅ | GLM系列模型 |
| 其他模型 | 🔄 | 基于LangBot框架可扩展 |

## 🛠️ 开发指南

### 项目结构

```
langbot/
├── pkg/                     # 核心代码
│   ├── core/               # 核心模块
│   │   ├── session/        # 会话管理
│   │   ├── workflow/       # 工作流路由
│   │   ├── image/          # 图像处理
│   │   └── intent/         # 意图分析
│   ├── workers/            # 工作流执行器
│   │   ├── flux/           # Flux工作流
│   │   ├── kontext/        # Kontext工作流
│   │   └── shared/         # 共享组件
│   ├── platform/           # 平台适配器
│   └── provider/           # 服务提供者
├── config/                 # 配置文件
├── docs/                   # 文档
├── workflows/              # ComfyUI工作流文件
└── templates/              # 模板文件
```

### 二次开发

1. **添加新工作流**: 参考 [工作流扩展指南](docs/workflow-extension-guide.md)
2. **平台适配**: 参考 [二次开发集成指南](docs/二次开发集成指南.md)
3. **配置管理**: 参考 [开发指南](docs/deployment/DEV_GUIDE.md)

## 📚 文档

- [📖 文档中心](docs/README.md) - 完整的文档索引
- [🏗️ 系统架构](docs/architecture/SYSTEM_DESIGN.md) - 系统设计文档
- [🔄 路由系统](docs/architecture/TWO_LEVEL_ROUTING_ARCHITECTURE.md) - 两级路由架构
- [🎨 ComfyUI集成](docs/COMFYUI_INTEGRATION.md) - ComfyUI集成指南
- [🚀 快速开始](docs/deployment/QUICK_START.md) - 快速部署指南
- [🔧 开发指南](docs/deployment/DEV_GUIDE.md) - 开发环境搭建

## 🤝 贡献

欢迎提交Issue和Pull Request！

- 提交Bug报告或功能请求
- 改进文档和代码
- 分享使用经验和最佳实践

详细贡献指南请参考 [贡献指南](docs/CONTRIBUTING.md)

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源。

## 🙏 致谢

- 感谢 [LangBot](https://github.com/RockChinQ/LangBot) 项目提供的优秀基础框架
- 感谢 [ComfyUI](https://github.com/comfyanonymous/ComfyUI) 项目提供的强大图像生成能力
- 感谢所有贡献者和社区成员的支持
