# 📖 LangBot 文档中心

欢迎来到LangBot文档中心！这里包含了项目的完整文档，帮助您快速了解和使用LangBot。

## 🚀 快速开始

- [快速启动指南](./deployment/QUICK_START.md) - 5分钟快速部署
- [开发环境搭建](./deployment/DEV_GUIDE.md) - 开发环境配置
- [Docker开发指南](./deployment/DOCKER_DEVELOPMENT.md) - Docker容器化部署

## 🏗️ 系统架构

- [系统设计文档](./architecture/SYSTEM_DESIGN.md) - 系统整体架构设计
- [两级路由架构](./architecture/TWO_LEVEL_ROUTING_ARCHITECTURE.md) - 智能路由系统设计
- [AIGEN LLM智能路由](./AIGEN_LLM_ROUTING_GUIDE.md) - AIGEN管道LLM智能路由指南
- [统一路由系统PRD](./architecture/PRD-********-UnifiedRoutingSystem.md) - 路由系统产品需求
- [工作流重构规划](./architecture/WORKFLOW_REFACTORING_PLAN.md) - 工作流架构设计

## 🎨 功能集成

- [ComfyUI集成指南](./COMFYUI_INTEGRATION.md) - ComfyUI图像生成集成
- [ComfyUI API密钥指南](./COMFYUI_API_KEY_SUCCESS_GUIDE.md) - API密钥配置
- [ComfyUI官方API指南](./COMFYUI_OFFICIAL_API_KEY_GUIDE.md) - 官方API使用
- [LoRA管理指南](./LORA_MANAGEMENT_GUIDE.md) - LoRA模型管理
- [LoRA使用策略](./LORA_USAGE_STRATEGY.md) - LoRA使用最佳实践

## 📋 用户指南

- [用户交互流程](./USER_INTERACTION_FLOW.md) - 用户使用流程说明
- [Kontext用户指南](./KONTEXT_USER_GUIDE.md) - Kontext工作流使用
- [Kontext重复生成指南](./KONTEXT_REPEAT_GENERATION_GUIDE.md) - 重复生成功能
- [帮助命令指南](./HELP_COMMAND_GUIDE.md) - 命令使用说明

## 🔧 开发指南

- [二次开发集成指南](./二次开发集成指南.md) - 二次开发说明
- [工作流扩展指南](./workflow-extension-guide.md) - 自定义工作流开发
- [工作流管理指南](./workflow-management-guide.md) - 工作流管理
- [贡献指南](./CONTRIBUTING.md) - 如何参与项目贡献

## 🔧 部署运维

- [Docker Compose修复](./deployment/DOCKER_COMPOSE_FIX.md) - Docker兼容性问题解决
- [服务器重启指南](./deployment/SERVER_RESTART_GUIDE.md) - 服务重启说明
- [管理员同步指南](./admin-sync-guide.md) - 管理员功能说明

## 📋 规划文档

- [开发计划](./planning/DEVELOPMENT_PLAN.md) - 项目开发计划
- [开发路线图](./planning/DEVELOPMENT_ROADMAP.md) - 功能路线图

## 📡 API文档

- [API规范 V1](./api-specs/API_V1.md) - REST API接口文档

## 🔍 故障排除

- [快速参考](./troubleshooting/quick-reference.md) - 常见问题快速解决
- [网络问题排除](./troubleshooting/langbot-wechatpad-network-issue.md) - 网络连接故障

## 📊 配置示例

- [统一路由使用示例](./unified_routing_usage_examples.md) - 路由配置示例
- [智能路由系统](./intelligent-routing-system.md) - 路由系统说明

## 🧪 测试文档

- [单元测试总结](./unit-testing-summary.md) - 测试覆盖情况

---

## 🔄 最近更新

### 2025-01-07
- 🧹 **项目文档清理完成**
  - 删除了过时的调试文件和临时修复文档
  - 重新组织了文档结构，将散落的文档归档到docs目录
  - 更新了README文件，反映当前的项目架构
  - 清理了重构相关的临时文档

### 2024-12-20
- 🚀 **统一路由系统实现完成**
  - 实现了两级路由架构：触发词识别 + LLM智能分析
  - 支持aigen、kontext、kontext_api三种工作流管道
  - 集成了智能提示词优化和参数分析

---

**维护人员**: AI Assistant
**最后更新**: 2025-01-07